"""
数学建模竞赛第四轮训练：生产过程中的决策问题
深度分析与解决方案

作者：AI助手
日期：2025-08-01
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
from scipy.optimize import minimize, brute
import seaborn as sns
from itertools import product
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ProductionDecisionAnalyzer:
    """生产过程决策分析器"""
    
    def __init__(self):
        self.results = {}
        
    def problem1_sampling_design(self, p0=0.1, alpha=0.05, beta=0.1, p1=0.15):
        """
        问题1：设计最优抽样检测方案
        
        参数:
        p0: 标称次品率 (10%)
        alpha: 第一类错误概率 (5% for 95%信度)
        beta: 第二类错误概率 (10% for 90%信度)
        p1: 备择假设的次品率
        """
        print("="*60)
        print("问题1：抽样检测方案设计")
        print("="*60)
        
        # 方法1：基于正态近似的样本量计算
        def calculate_sample_size_normal(p0, p1, alpha, beta):
            z_alpha = stats.norm.ppf(1 - alpha)
            z_beta = stats.norm.ppf(1 - beta)
            
            # 样本量公式
            numerator = (z_alpha * np.sqrt(p0 * (1 - p0)) + z_beta * np.sqrt(p1 * (1 - p1)))**2
            denominator = (p1 - p0)**2
            n = numerator / denominator
            return int(np.ceil(n))
        
        # 方法2：精确的二项分布计算
        def find_optimal_sample_size(p0, p1, alpha, beta):
            best_n = float('inf')
            best_c = 0
            
            for n in range(10, 1000):
                for c in range(1, n):
                    # 计算第一类错误概率
                    alpha_actual = 1 - stats.binom.cdf(c-1, n, p0)
                    # 计算第二类错误概率  
                    beta_actual = stats.binom.cdf(c-1, n, p1)
                    
                    if alpha_actual <= alpha and beta_actual <= beta:
                        if n < best_n:
                            best_n = n
                            best_c = c
                        break
            
            return best_n, best_c
        
        # 情形1：95%信度下拒收
        print("\n情形1：在95%信度下认定零配件次品率超过标称值，则拒收这批零配件")
        n1_normal = calculate_sample_size_normal(p0, p1, alpha, beta)
        n1_exact, c1_exact = find_optimal_sample_size(p0, p1, alpha, beta)
        
        print(f"正态近似方法：样本量 n = {n1_normal}")
        print(f"精确计算方法：样本量 n = {n1_exact}, 临界值 c = {c1_exact}")
        
        # 验证第一类错误概率
        alpha_verify = 1 - stats.binom.cdf(c1_exact-1, n1_exact, p0)
        print(f"实际第一类错误概率：{alpha_verify:.4f} (应 ≤ {alpha})")
        
        # 情形2：90%信度下接收
        print("\n情形2：在90%信度下认定零配件次品率不超过标称值，则接收这批零配件")
        beta2 = 0.1  # 90%信度对应10%的第二类错误
        n2_exact, c2_exact = find_optimal_sample_size(p0, p1, alpha, beta2)
        
        print(f"样本量 n = {n2_exact}, 临界值 c = {c2_exact}")
        
        # 验证第二类错误概率
        beta_verify = stats.binom.cdf(c2_exact-1, n2_exact, p1)
        print(f"实际第二类错误概率：{beta_verify:.4f} (应 ≤ {beta2})")
        
        # 绘制功效函数
        self.plot_power_function(n1_exact, c1_exact, p0)
        
        self.results['problem1'] = {
            'case1': {'n': n1_exact, 'c': c1_exact, 'alpha': alpha_verify},
            'case2': {'n': n2_exact, 'c': c2_exact, 'beta': beta_verify}
        }
        
        return n1_exact, c1_exact, n2_exact, c2_exact
    
    def plot_power_function(self, n, c, p0):
        """绘制检验的功效函数"""
        p_values = np.linspace(0, 0.3, 100)
        power = [1 - stats.binom.cdf(c-1, n, p) for p in p_values]
        
        plt.figure(figsize=(10, 6))
        plt.plot(p_values, power, 'b-', linewidth=2, label='功效函数')
        plt.axvline(x=p0, color='r', linestyle='--', label=f'标称值 p₀={p0}')
        plt.axhline(y=0.95, color='g', linestyle='--', label='95%信度线')
        plt.xlabel('真实次品率 p')
        plt.ylabel('拒收概率（功效）')
        plt.title(f'抽样检测方案的功效函数 (n={n}, c={c})')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('power_function.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def problem2_production_decision(self, params):
        """
        问题2：生产过程各阶段决策优化
        
        参数字典应包含：
        - p1, p2: 零配件1和2的次品率
        - p3: 装配后成品的次品率
        - c1, c2: 零配件1和2的购买单价
        - d1, d2: 零配件1和2的检测成本
        - d3: 成品检测成本
        - a: 装配成本
        - s: 市场售价
        - b: 调换损失
        - u: 拆解费用
        """
        print("\n" + "="*60)
        print("问题2：生产过程决策优化")
        print("="*60)
        
        def calculate_expected_profit(decisions, params):
            """计算给定决策下的期望利润"""
            x1, x2, y, z = decisions  # 决策变量
            p1, p2, p3 = params['p1'], params['p2'], params['p3']
            c1, c2 = params['c1'], params['c2']
            d1, d2, d3 = params['d1'], params['d2'], params['d3']
            a, s, b, u = params['a'], params['s'], params['b'], params['u']
            
            # 基础成本
            cost = c1 + c2 + a  # 购买成本 + 装配成本
            
            # 检测成本
            if x1: cost += d1
            if x2: cost += d2
            if y: cost += d3
            
            # 零配件合格率（经过检测后）
            q1 = 1 - x1 * p1  # 零配件1合格率
            q2 = 1 - x2 * p2  # 零配件2合格率
            
            # 装配后合格率
            q_assembly = q1 * q2 * (1 - p3)
            
            # 进入市场的产品合格率
            if y:  # 如果检测成品
                q_market = q_assembly  # 只有合格品进入市场
                # 不合格成品的处理
                defect_rate = 1 - q_assembly
                if z:  # 拆解不合格品
                    cost += defect_rate * u  # 拆解成本
                    # 拆解后零配件的回收价值（简化处理）
                    recovery_value = defect_rate * 0.5 * (c1 + c2)
                    cost -= recovery_value
            else:  # 如果不检测成品
                q_market = q_assembly  # 所有产品进入市场
            
            # 收入和调换损失
            revenue = s  # 销售收入
            replacement_loss = (1 - q_market) * b  # 调换损失
            
            # 期望利润
            expected_profit = revenue - cost - replacement_loss
            
            return expected_profit
        
        # 枚举所有可能的决策组合
        decisions_space = list(product([0, 1], repeat=4))  # x1, x2, y, z
        
        best_profit = -float('inf')
        best_decision = None
        results_table = []
        
        for decisions in decisions_space:
            profit = calculate_expected_profit(decisions, params)
            results_table.append({
                '检测零配件1': '是' if decisions[0] else '否',
                '检测零配件2': '是' if decisions[1] else '否', 
                '检测成品': '是' if decisions[2] else '否',
                '拆解不合格品': '是' if decisions[3] else '否',
                '期望利润': profit
            })
            
            if profit > best_profit:
                best_profit = profit
                best_decision = decisions
        
        # 显示结果
        df_results = pd.DataFrame(results_table)
        df_results = df_results.sort_values('期望利润', ascending=False)
        
        print("\n所有决策方案的期望利润排序：")
        print(df_results.to_string(index=False))
        
        print(f"\n最优决策方案：")
        print(f"检测零配件1: {'是' if best_decision[0] else '否'}")
        print(f"检测零配件2: {'是' if best_decision[1] else '否'}")
        print(f"检测成品: {'是' if best_decision[2] else '否'}")
        print(f"拆解不合格品: {'是' if best_decision[3] else '否'}")
        print(f"最大期望利润: {best_profit:.2f}")
        
        self.results['problem2'] = {
            'best_decision': best_decision,
            'best_profit': best_profit,
            'all_results': df_results
        }
        
        return best_decision, best_profit, df_results

def main():
    """主函数"""
    analyzer = ProductionDecisionAnalyzer()
    
    # 问题1：抽样检测方案设计
    n1, c1, n2, c2 = analyzer.problem1_sampling_design()
    
    # 问题2：示例参数（需要根据实际表1数据调整）
    example_params = {
        'p1': 0.1,    # 零配件1次品率
        'p2': 0.1,    # 零配件2次品率  
        'p3': 0.1,    # 装配后成品次品率
        'c1': 4,      # 零配件1购买单价
        'c2': 18,     # 零配件2购买单价
        'd1': 2,      # 零配件1检测成本
        'd2': 3,      # 零配件2检测成本
        'd3': 3,      # 成品检测成本
        'a': 6,       # 装配成本
        's': 56,      # 市场售价
        'b': 6,       # 调换损失
        'u': 5        # 拆解费用
    }
    
    # 问题2：生产过程决策优化
    best_decision, best_profit, results_df = analyzer.problem2_production_decision(example_params)
    
    print("\n" + "="*60)
    print("分析总结")
    print("="*60)
    print("本分析提供了完整的数学建模解决方案，包括：")
    print("1. 基于统计假设检验的最优抽样检测方案")
    print("2. 多阶段生产过程的决策优化模型")
    print("3. 期望利润最大化的决策框架")
    print("\n建议进一步进行敏感性分析和参数优化。")

if __name__ == "__main__":
    main()
