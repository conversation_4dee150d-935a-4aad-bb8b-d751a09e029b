import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.optimize import minimize_scalar
import datetime
from itertools import product

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class AdvancedSamplingDesigner:
    """高级抽样检测方案设计器"""
    
    def __init__(self):
        self.timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.results = {}
        
    def single_sampling_plan(self, p0, p1, alpha, beta):
        """
        单次抽样方案设计
        返回最优的样本量n和临界值c
        """
        best_n = float('inf')
        best_c = 0
        
        # 搜索最优解
        for n in range(10, 2000):
            for c in range(1, n+1):
                # 第一类错误概率：P(拒收|H0为真)
                alpha_actual = 1 - stats.binom.cdf(c-1, n, p0)
                # 第二类错误概率：P(接收|H1为真)
                beta_actual = stats.binom.cdf(c-1, n, p1)
                
                if alpha_actual <= alpha and beta_actual <= beta:
                    if n < best_n:
                        best_n = n
                        best_c = c
                    break
        
        return best_n, best_c
    
    def double_sampling_plan(self, p0, p1, alpha, beta):
        """
        双重抽样方案设计（简化版本）
        返回两阶段的样本量和临界值
        """
        # 简化搜索，只考虑几个典型的双重抽样方案
        print("   双重抽样方案搜索中...")

        # 基于单次抽样结果设计双重方案
        n_single, c_single = self.single_sampling_plan(p0, p1, alpha, beta)

        # 简化的双重抽样方案：第一阶段用一半样本
        n1 = n_single // 2
        n2 = n_single - n1
        c1 = max(1, c_single // 3)  # 第一阶段接收临界值
        r1 = min(n1, c_single * 2 // 3)  # 第一阶段拒收临界值
        c2 = c_single  # 总的接收临界值

        # 验证方案可行性
        try:
            oc_p0 = self.calculate_double_oc(n1, n2, c1, r1, c2, p0)
            oc_p1 = self.calculate_double_oc(n1, n2, c1, r1, c2, p1)

            if (1-oc_p0) <= alpha and oc_p1 <= beta:
                asn_p0 = self.calculate_double_asn(n1, n2, c1, r1, p0)
                asn_p1 = self.calculate_double_asn(n1, n2, c1, r1, p1)
                avg_asn = (asn_p0 + asn_p1) / 2
                return (n1, n2, c1, r1, c2), avg_asn
        except:
            pass

        return None, float('inf')
    
    def calculate_double_oc(self, n1, n2, c1, r1, c2, p):
        """计算双重抽样的操作特征值"""
        # 第一阶段接收
        prob_accept_1 = stats.binom.cdf(c1, n1, p)
        
        # 第一阶段拒收
        prob_reject_1 = 1 - stats.binom.cdf(r1-1, n1, p)
        
        # 第二阶段
        prob_continue = 1 - prob_accept_1 - prob_reject_1
        prob_accept_2 = 0
        
        for x1 in range(c1+1, r1):
            prob_x1 = stats.binom.pmf(x1, n1, p)
            prob_accept_given_x1 = stats.binom.cdf(c2-x1, n2, p)
            prob_accept_2 += prob_x1 * prob_accept_given_x1
        
        return prob_accept_1 + prob_accept_2
    
    def calculate_double_asn(self, n1, n2, c1, r1, p):
        """计算双重抽样的平均样本数量"""
        prob_continue = 0
        for x1 in range(c1+1, r1):
            prob_continue += stats.binom.pmf(x1, n1, p)
        
        return n1 + n2 * prob_continue
    
    def sequential_probability_ratio_test(self, p0, p1, alpha, beta):
        """
        序贯概率比检验(SPRT)设计
        """
        # 计算决策边界
        A = (1 - beta) / alpha
        B = beta / (1 - alpha)
        
        # 对数似然比的系数
        h1 = np.log(A)
        h0 = np.log(B)
        
        # 每次观察的对数似然比增量期望
        delta = p1 * np.log(p1/p0) + (1-p1) * np.log((1-p1)/(1-p0))
        
        # 平均样本数量近似
        if delta > 0:
            asn_p0 = (h0 * (1-alpha) + h1 * alpha) / delta
            asn_p1 = (h0 * beta + h1 * (1-beta)) / delta
        else:
            asn_p0 = asn_p1 = float('inf')
        
        return h0, h1, asn_p0, asn_p1
    
    def comprehensive_analysis(self):
        """
        问题1的综合深度分析
        """
        print("="*80)
        print("问题1深度分析：抽样检测方案设计的高级优化")
        print("="*80)
        
        # 基本参数
        p0 = 0.1  # 标称值
        p1 = 0.15  # 备择假设值（可调整）
        
        # 情形1：95%信度下拒收
        print("\n情形1：在95%的信度下认定零配件次品率超过标称值，则拒收这批零配件")
        alpha1 = 0.05
        beta1 = 0.10
        
        # 情形2：90%信度下接收  
        print("\n情形2：在90%的信度下认定零配件次品率不超过标称值，则接收这批零配件")
        alpha2 = 0.10
        beta2 = 0.10
        
        # 分析两种情形
        results = {}
        
        for case_name, alpha, beta in [("情形1", alpha1, beta1), ("情形2", alpha2, beta2)]:
            print(f"\n{'-'*50}")
            print(f"{case_name}分析")
            print(f"{'-'*50}")
            
            # 单次抽样方案
            n_single, c_single = self.single_sampling_plan(p0, p1, alpha, beta)
            print(f"单次抽样方案：n={n_single}, c={c_single}")
            
            # 双重抽样方案
            double_plan, double_asn = self.double_sampling_plan(p0, p1, alpha, beta)
            if double_plan:
                n1, n2, c1, r1, c2 = double_plan
                print(f"双重抽样方案：n1={n1}, n2={n2}, c1={c1}, r1={r1}, c2={c2}")
                print(f"平均样本数量：{double_asn:.1f}")
            else:
                print("未找到满足条件的双重抽样方案")
            
            # 序贯检验
            h0, h1, asn_p0, asn_p1 = self.sequential_probability_ratio_test(p0, p1, alpha, beta)
            print(f"序贯检验边界：h0={h0:.3f}, h1={h1:.3f}")
            print(f"序贯检验平均样本数量：ASN(p0)={asn_p0:.1f}, ASN(p1)={asn_p1:.1f}")
            
            results[case_name] = {
                'single': (n_single, c_single),
                'double': double_plan,
                'sequential': (h0, h1, asn_p0, asn_p1),
                'alpha': alpha,
                'beta': beta
            }
        
        self.results = results
        
        # 生成详细分析图表
        self.generate_comprehensive_plots(p0, p1)
        
        # 生成决策建议
        self.generate_recommendations()
        
        return results
    
    def generate_comprehensive_plots(self, p0, p1):
        """生成综合分析图表"""
        
        # 图1：操作特征曲线比较
        self.plot_oc_curves_comparison(p0, p1)
        
        # 图2：平均样本数量曲线
        self.plot_asn_curves()
        
        # 图3：成本效益分析
        self.plot_cost_benefit_analysis()
        
        # 图4：方案比较表
        self.plot_plan_comparison_table()
    
    def plot_oc_curves_comparison(self, p0, p1):
        """绘制操作特征曲线比较"""
        plt.figure(figsize=(12, 8))
        
        p_range = np.linspace(0, 0.25, 100)
        
        for case_name, case_data in self.results.items():
            n_single, c_single = case_data['single']
            
            # 计算OC曲线
            oc_values = [stats.binom.cdf(c_single-1, n_single, p) for p in p_range]
            
            plt.plot(p_range, oc_values, linewidth=2, label=f'{case_name} (n={n_single}, c={c_single})')
        
        # 添加关键点
        plt.axvline(x=p0, color='red', linestyle='--', alpha=0.7, label=f'标称值 p₀={p0}')
        plt.axvline(x=p1, color='orange', linestyle='--', alpha=0.7, label=f'备择值 p₁={p1}')
        plt.axhline(y=0.95, color='green', linestyle=':', alpha=0.7, label='95%接收线')
        plt.axhline(y=0.90, color='blue', linestyle=':', alpha=0.7, label='90%接收线')
        
        plt.xlabel('真实次品率 p')
        plt.ylabel('接收概率')
        plt.title(f'操作特征曲线比较 - {self.timestamp}_问题1')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        filename = f'{self.timestamp}_问题1_操作特征曲线比较.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"已保存图表：{filename}")
    
    def plot_asn_curves(self):
        """绘制平均样本数量曲线"""
        plt.figure(figsize=(12, 8))
        
        p_range = np.linspace(0.05, 0.20, 50)
        
        for case_name, case_data in self.results.items():
            n_single, c_single = case_data['single']
            
            # 单次抽样的ASN就是固定的n
            asn_values = [n_single] * len(p_range)
            
            plt.plot(p_range, asn_values, linewidth=2, label=f'{case_name} 单次抽样')
            
            # 如果有序贯检验结果
            if case_data['sequential'][2] != float('inf'):
                h0, h1, asn_p0, asn_p1 = case_data['sequential']
                # 简化的ASN曲线（线性插值）
                asn_seq = []
                for p in p_range:
                    if p <= 0.1:
                        asn_seq.append(asn_p0)
                    else:
                        # 线性插值
                        ratio = (p - 0.1) / (0.15 - 0.1)
                        asn_seq.append(asn_p0 + ratio * (asn_p1 - asn_p0))
                
                plt.plot(p_range, asn_seq, '--', linewidth=2, label=f'{case_name} 序贯检验')
        
        plt.xlabel('真实次品率 p')
        plt.ylabel('平均样本数量')
        plt.title(f'平均样本数量曲线 - {self.timestamp}_问题1')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        filename = f'{self.timestamp}_问题1_平均样本数量曲线.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"已保存图表：{filename}")
    
    def plot_cost_benefit_analysis(self):
        """绘制成本效益分析"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # 假设检测成本
        cost_per_sample = 5  # 每个样本的检测成本
        
        # 成本比较
        case_names = list(self.results.keys())
        single_costs = []
        single_samples = []
        
        for case_name in case_names:
            n_single, c_single = self.results[case_name]['single']
            single_costs.append(n_single * cost_per_sample)
            single_samples.append(n_single)
        
        # 柱状图1：样本量比较
        bars1 = ax1.bar(case_names, single_samples, alpha=0.7, color=['skyblue', 'lightcoral'])
        ax1.set_ylabel('样本量')
        ax1.set_title('样本量比较')
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, value in zip(bars1, single_samples):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{value}', ha='center', va='bottom')
        
        # 柱状图2：检测成本比较
        bars2 = ax2.bar(case_names, single_costs, alpha=0.7, color=['lightgreen', 'lightyellow'])
        ax2.set_ylabel('检测成本（元）')
        ax2.set_title('检测成本比较')
        ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, value in zip(bars2, single_costs):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 10,
                    f'{value}', ha='center', va='bottom')
        
        plt.suptitle(f'成本效益分析 - {self.timestamp}_问题1')
        plt.tight_layout()
        
        filename = f'{self.timestamp}_问题1_成本效益分析.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"已保存图表：{filename}")
    
    def plot_plan_comparison_table(self):
        """绘制方案比较表"""
        # 创建比较数据
        comparison_data = []
        
        for case_name, case_data in self.results.items():
            n_single, c_single = case_data['single']
            
            comparison_data.append({
                '情形': case_name,
                '抽样方案': '单次抽样',
                '样本量(n)': n_single,
                '临界值(c)': c_single,
                '第一类错误(α)': case_data['alpha'],
                '第二类错误(β)': case_data['beta'],
                '决策规则': f'次品数≥{c_single}时拒收'
            })
        
        df = pd.DataFrame(comparison_data)
        
        # 创建表格图
        fig, ax = plt.subplots(figsize=(14, 6))
        ax.axis('tight')
        ax.axis('off')
        
        table = ax.table(cellText=df.values,
                        colLabels=df.columns,
                        cellLoc='center',
                        loc='center',
                        bbox=[0, 0, 1, 1])
        
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 2)
        
        # 设置表格样式
        for i in range(len(df.columns)):
            table[(0, i)].set_facecolor('#4CAF50')
            table[(0, i)].set_text_props(weight='bold', color='white')
        
        for i in range(1, len(df) + 1):
            for j in range(len(df.columns)):
                if i % 2 == 0:
                    table[(i, j)].set_facecolor('#f0f0f0')
        
        plt.title(f'抽样检测方案比较表 - {self.timestamp}_问题1', 
                 fontsize=14, fontweight='bold', pad=20)
        
        filename = f'{self.timestamp}_问题1_方案比较表.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"已保存图表：{filename}")
    
    def generate_recommendations(self):
        """生成决策建议"""
        print(f"\n{'='*80}")
        print("决策建议与实施指导")
        print(f"{'='*80}")
        
        print("\n1. 方案选择建议：")
        for case_name, case_data in self.results.items():
            n_single, c_single = case_data['single']
            print(f"   {case_name}：推荐使用单次抽样方案")
            print(f"   - 样本量：{n_single}")
            print(f"   - 临界值：{c_single}")
            print(f"   - 决策规则：当检测到的次品数≥{c_single}时拒收该批零配件")
        
        print(f"\n2. 实施要点：")
        print("   - 确保样本的随机性和代表性")
        print("   - 建立标准化的检测流程")
        print("   - 记录检测结果用于后续分析")
        print("   - 定期评估和调整检测方案")
        
        print(f"\n3. 成本控制：")
        print("   - 平衡检测成本与质量风险")
        print("   - 考虑批量大小对检测效率的影响")
        print("   - 建立供应商质量改进激励机制")

def main():
    """主函数"""
    designer = AdvancedSamplingDesigner()
    
    print("开始问题1的深度分析...")
    results = designer.comprehensive_analysis()
    
    print(f"\n{'='*80}")
    print("分析完成！")
    print(f"{'='*80}")
    print(f"时间戳：{designer.timestamp}")
    print("已生成以下文件：")
    print(f"1. {designer.timestamp}_问题1_操作特征曲线比较.png")
    print(f"2. {designer.timestamp}_问题1_平均样本数量曲线.png")
    print(f"3. {designer.timestamp}_问题1_成本效益分析.png")
    print(f"4. {designer.timestamp}_问题1_方案比较表.png")

if __name__ == "__main__":
    main()
