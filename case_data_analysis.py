"""
具体案例数据分析模块
基于PDF中表1和表2的具体数据进行深度分析
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from production_decision_analysis import ProductionDecisionAnalyzer
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class CaseDataAnalyzer:
    """具体案例数据分析器"""
    
    def __init__(self):
        self.table1_data = self.load_table1_data()
        self.table2_data = self.load_table2_data()
        self.base_analyzer = ProductionDecisionAnalyzer()
    
    def load_table1_data(self):
        """加载表1数据：企业在生产中遇到的情形（问题2）"""
        data = {
            'case': [1, 2, 3, 4, 5, 6],
            # 零配件1
            'p1': [0.10, 0.20, 0.10, 0.20, 0.10, 0.05],  # 次品率
            'c1': [4, 4, 4, 4, 4, 4],                     # 购买单价
            'd1': [2, 2, 2, 1, 6, 2],                     # 检测成本
            # 零配件2  
            'p2': [0.10, 0.20, 0.10, 0.20, 0.20, 0.05],  # 次品率
            'c2': [18, 18, 18, 18, 18, 18],               # 购买单价
            'd2': [3, 3, 3, 1, 1, 3],                     # 检测成本
            # 成品
            'p3': [0.10, 0.20, 0.10, 0.20, 0.10, 0.05],  # 次品率
            'd3': [6, 6, 6, 2, 2, 3],                     # 检测成本
            'a': [3, 3, 3, 2, 2, 3],                      # 装配成本
            's': [56, 56, 56, 56, 56, 56],                # 市场售价
            'b': [6, 6, 30, 30, 10, 10],                  # 调换损失
            'u': [5, 5, 5, 5, 5, 30]                      # 拆解费用
        }
        return pd.DataFrame(data)
    
    def load_table2_data(self):
        """加载表2数据：企业在生产中遇到的情形（问题3）"""
        data = {
            'component': [1, 2, 3, 4, 5, 6, 7, 8],
            'defect_rate': [0.10] * 8,  # 所有零配件次品率都是10%
            'purchase_cost': [10, 10, 10, 10, 10, 10, 10, 10],  # 购买单价
            'inspection_cost': [2, 8, 12, 2, 8, 12, 8, 12],     # 检测成本
            'assembly_cost': [1, 1, 2, 1, 1, 2, 1, 2],          # 装配成本
            'stage': ['半成品1', '半成品1', '半成品1', '半成品2', '半成品2', '半成品2', '半成品3', '半成品3']
        }
        
        # 半成品和成品信息
        product_data = {
            'product_type': ['半成品1', '半成品2', '半成品3', '成品'],
            'defect_rate': [0.10, 0.10, 0.10, 0.10],
            'inspection_cost': [8, 8, 8, 6],
            'market_price': [None, None, None, 200],
            'replacement_loss': [4, 4, 4, 30]
        }
        
        return pd.DataFrame(data), pd.DataFrame(product_data)
    
    def analyze_table1_cases(self):
        """分析表1中的6种情形"""
        print("="*80)
        print("表1案例分析：企业生产中的6种情形")
        print("="*80)
        
        results = []
        
        for idx, row in self.table1_data.iterrows():
            case_num = row['case']
            print(f"\n情形{case_num}分析：")
            print("-" * 40)
            
            # 构建参数字典
            params = {
                'p1': row['p1'], 'p2': row['p2'], 'p3': row['p3'],
                'c1': row['c1'], 'c2': row['c2'],
                'd1': row['d1'], 'd2': row['d2'], 'd3': row['d3'],
                'a': row['a'], 's': row['s'], 'b': row['b'], 'u': row['u']
            }
            
            # 进行决策分析
            best_decision, best_profit, _ = self.base_analyzer.problem2_production_decision(params)
            
            # 记录结果
            result = {
                'case': case_num,
                'best_decision': best_decision,
                'best_profit': best_profit,
                'params': params
            }
            results.append(result)
            
            # 输出决策建议
            print(f"最优决策：")
            print(f"  检测零配件1: {'是' if best_decision[0] else '否'}")
            print(f"  检测零配件2: {'是' if best_decision[1] else '否'}")
            print(f"  检测成品: {'是' if best_decision[2] else '否'}")
            print(f"  拆解不合格品: {'是' if best_decision[3] else '否'}")
            print(f"  最大期望利润: {best_profit:.2f}")
            
            # 分析决策依据
            self.analyze_decision_rationale(params, best_decision, best_profit)
        
        # 生成对比分析
        self.compare_all_cases(results)
        
        return results
    
    def analyze_decision_rationale(self, params, decision, profit):
        """分析决策依据"""
        print(f"\n决策依据分析：")
        
        # 计算各项成本占比
        total_component_cost = params['c1'] + params['c2']
        inspection_cost = (decision[0] * params['d1'] + 
                          decision[1] * params['d2'] + 
                          decision[2] * params['d3'])
        
        print(f"  零配件成本: {total_component_cost}")
        print(f"  检测成本: {inspection_cost}")
        print(f"  装配成本: {params['a']}")
        print(f"  市场售价: {params['s']}")
        print(f"  调换损失风险: {params['b']}")
        
        # 质量风险分析
        total_defect_risk = 1 - (1 - params['p1']) * (1 - params['p2']) * (1 - params['p3'])
        print(f"  总体次品风险: {total_defect_risk:.1%}")
        
        # 成本效益比分析
        if inspection_cost > 0:
            cost_benefit_ratio = inspection_cost / max(params['b'], 1)
            print(f"  检测成本/调换损失比: {cost_benefit_ratio:.2f}")
    
    def compare_all_cases(self, results):
        """对比分析所有情形"""
        print("\n" + "="*80)
        print("所有情形对比分析")
        print("="*80)
        
        # 创建对比表
        comparison_data = []
        for result in results:
            case = result['case']
            decision = result['best_decision']
            profit = result['best_profit']
            params = result['params']
            
            comparison_data.append({
                '情形': case,
                '检测零配件1': '是' if decision[0] else '否',
                '检测零配件2': '是' if decision[1] else '否',
                '检测成品': '是' if decision[2] else '否',
                '拆解不合格品': '是' if decision[3] else '否',
                '期望利润': profit,
                '零配件1次品率': f"{params['p1']:.0%}",
                '零配件2次品率': f"{params['p2']:.0%}",
                '调换损失': params['b']
            })
        
        df_comparison = pd.DataFrame(comparison_data)
        print(df_comparison.to_string(index=False))
        
        # 绘制对比图
        self.plot_case_comparison(df_comparison)
        
        # 分析规律
        self.analyze_decision_patterns(df_comparison)
    
    def plot_case_comparison(self, df):
        """绘制情形对比图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 期望利润对比
        bars1 = ax1.bar(df['情形'], df['期望利润'], color='skyblue', alpha=0.7)
        ax1.set_xlabel('情形')
        ax1.set_ylabel('期望利润')
        ax1.set_title('各情形期望利润对比')
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, profit in zip(bars1, df['期望利润']):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{profit:.1f}', ha='center', va='bottom')
        
        # 决策模式热力图
        decision_matrix = np.zeros((len(df), 4))
        for i, row in df.iterrows():
            decision_matrix[i, 0] = 1 if row['检测零配件1'] == '是' else 0
            decision_matrix[i, 1] = 1 if row['检测零配件2'] == '是' else 0
            decision_matrix[i, 2] = 1 if row['检测成品'] == '是' else 0
            decision_matrix[i, 3] = 1 if row['拆解不合格品'] == '是' else 0
        
        sns.heatmap(decision_matrix.T,
                   xticklabels=[f'情形{i}' for i in df['情形']],
                   yticklabels=['检测零配件1', '检测零配件2', '检测成品', '拆解不合格品'],
                   annot=True, fmt='.0f', cmap='RdYlBu_r', ax=ax2)
        ax2.set_title('决策模式热力图')
        
        # 调换损失影响分析
        ax3.scatter(df['调换损失'], df['期望利润'], s=100, alpha=0.7, color='orange')
        ax3.set_xlabel('调换损失')
        ax3.set_ylabel('期望利润')
        ax3.set_title('调换损失对期望利润的影响')
        ax3.grid(True, alpha=0.3)
        
        # 为每个点添加标签
        for i, row in df.iterrows():
            ax3.annotate(f'情形{row["情形"]}', 
                        (row['调换损失'], row['期望利润']),
                        xytext=(5, 5), textcoords='offset points')
        
        # 决策频率统计
        decision_counts = {
            '检测零配件1': sum(1 for x in df['检测零配件1'] if x == '是'),
            '检测零配件2': sum(1 for x in df['检测零配件2'] if x == '是'),
            '检测成品': sum(1 for x in df['检测成品'] if x == '是'),
            '拆解不合格品': sum(1 for x in df['拆解不合格品'] if x == '是')
        }
        
        ax4.bar(decision_counts.keys(), decision_counts.values(), color='lightgreen', alpha=0.7)
        ax4.set_ylabel('选择频次')
        ax4.set_title('各决策选项的选择频次')
        ax4.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig('case_comparison_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def analyze_decision_patterns(self, df):
        """分析决策规律"""
        print("\n决策规律分析：")
        print("-" * 40)
        
        # 分析高利润情形的特征
        high_profit_cases = df[df['期望利润'] > df['期望利润'].median()]
        print(f"高利润情形（>{df['期望利润'].median():.1f}）的特征：")
        
        for decision_type in ['检测零配件1', '检测零配件2', '检测成品', '拆解不合格品']:
            yes_count = sum(1 for x in high_profit_cases[decision_type] if x == '是')
            total_count = len(high_profit_cases)
            percentage = yes_count / total_count * 100 if total_count > 0 else 0
            print(f"  {decision_type}: {percentage:.0f}% 选择'是'")
        
        # 分析调换损失的影响
        high_loss_cases = df[df['调换损失'] >= 10]
        low_loss_cases = df[df['调换损失'] < 10]
        
        print(f"\n调换损失影响分析：")
        print(f"高调换损失情形（≥10）平均利润: {high_loss_cases['期望利润'].mean():.2f}")
        print(f"低调换损失情形（<10）平均利润: {low_loss_cases['期望利润'].mean():.2f}")

def main():
    """主函数"""
    analyzer = CaseDataAnalyzer()
    
    print("数学建模竞赛第四轮训练：生产过程中的决策问题")
    print("基于PDF表1和表2数据的深度分析")
    print("="*80)
    
    # 分析表1的6种情形
    table1_results = analyzer.analyze_table1_cases()
    
    print("\n" + "="*80)
    print("分析总结与建议")
    print("="*80)
    print("1. 决策优化建议：")
    print("   - 当调换损失较高时，应增加检测环节")
    print("   - 零配件次品率差异较大时，重点检测高风险零配件")
    print("   - 检测成本与调换损失的权衡是关键决策因素")
    
    print("\n2. 模型应用价值：")
    print("   - 为企业提供科学的质量控制决策依据")
    print("   - 帮助优化生产成本和质量风险的平衡")
    print("   - 支持不同生产情形下的灵活决策")

if __name__ == "__main__":
    main()
