"""
问题1优化分析：抽样检测方案设计
专注于单次抽样方案的精确设计和深度分析
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class OptimizedSamplingAnalyzer:
    """优化的抽样检测分析器"""
    
    def __init__(self):
        self.timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.results = {}
        
    def find_optimal_single_sampling(self, p0, p1, alpha, beta):
        """
        寻找最优单次抽样方案
        使用精确的二项分布计算
        """
        best_n = float('inf')
        best_c = 0
        
        # 搜索最优解
        for n in range(10, 1000):
            for c in range(1, n+1):
                # 第一类错误概率：P(拒收|H0为真) = P(X≥c|p=p0)
                alpha_actual = 1 - stats.binom.cdf(c-1, n, p0)
                # 第二类错误概率：P(接收|H1为真) = P(X<c|p=p1)
                beta_actual = stats.binom.cdf(c-1, n, p1)
                
                if alpha_actual <= alpha and beta_actual <= beta:
                    if n < best_n:
                        best_n = n
                        best_c = c
                    break
        
        return best_n, best_c
    
    def calculate_oc_function(self, n, c, p_range):
        """计算操作特征函数"""
        return [stats.binom.cdf(c-1, n, p) for p in p_range]
    
    def calculate_power_function(self, n, c, p_range):
        """计算功效函数"""
        return [1 - stats.binom.cdf(c-1, n, p) for p in p_range]
    
    def comprehensive_analysis(self):
        """
        问题1的综合分析
        """
        print("="*80)
        print("问题1深度分析：抽样检测方案设计")
        print("="*80)
        
        # 基本参数
        p0 = 0.1   # 标称值（零假设）
        p1 = 0.15  # 备择假设值
        
        print(f"标称次品率 p₀ = {p0}")
        print(f"备择次品率 p₁ = {p1}")
        
        # 情形1：95%信度下拒收
        print(f"\n{'-'*60}")
        print("情形1：在95%的信度下认定零配件次品率超过标称值，则拒收这批零配件")
        print(f"{'-'*60}")
        
        alpha1 = 0.05  # 第一类错误概率
        beta1 = 0.10   # 第二类错误概率（假设）
        
        n1, c1 = self.find_optimal_single_sampling(p0, p1, alpha1, beta1)
        
        # 验证错误概率
        alpha1_actual = 1 - stats.binom.cdf(c1-1, n1, p0)
        beta1_actual = stats.binom.cdf(c1-1, n1, p1)
        
        print(f"最优抽样方案：")
        print(f"  样本量 n = {n1}")
        print(f"  临界值 c = {c1}")
        print(f"  决策规则：当观察到的次品数 ≥ {c1} 时拒收该批零配件")
        print(f"验证结果：")
        print(f"  实际第一类错误概率 α = {alpha1_actual:.4f} (要求 ≤ {alpha1})")
        print(f"  实际第二类错误概率 β = {beta1_actual:.4f} (设定 ≤ {beta1})")
        
        # 情形2：90%信度下接收
        print(f"\n{'-'*60}")
        print("情形2：在90%的信度下认定零配件次品率不超过标称值，则接收这批零配件")
        print(f"{'-'*60}")
        
        # 对于情形2，我们需要重新理解题意
        # 90%信度下接收意味着：当真实次品率为p0时，有90%的概率接收
        # 这相当于第一类错误概率为10%
        alpha2 = 0.10  # 第一类错误概率
        beta2 = 0.10   # 第二类错误概率
        
        n2, c2 = self.find_optimal_single_sampling(p0, p1, alpha2, beta2)
        
        # 验证错误概率
        alpha2_actual = 1 - stats.binom.cdf(c2-1, n2, p0)
        beta2_actual = stats.binom.cdf(c2-1, n2, p1)
        
        print(f"最优抽样方案：")
        print(f"  样本量 n = {n2}")
        print(f"  临界值 c = {c2}")
        print(f"  决策规则：当观察到的次品数 < {c2} 时接收该批零配件")
        print(f"验证结果：")
        print(f"  实际第一类错误概率 α = {alpha2_actual:.4f} (要求 ≤ {alpha2})")
        print(f"  实际第二类错误概率 β = {beta2_actual:.4f} (设定 ≤ {beta2})")
        
        # 保存结果
        self.results = {
            '情形1': {
                'n': n1, 'c': c1, 'alpha': alpha1_actual, 'beta': beta1_actual,
                'alpha_target': alpha1, 'beta_target': beta1
            },
            '情形2': {
                'n': n2, 'c': c2, 'alpha': alpha2_actual, 'beta': beta2_actual,
                'alpha_target': alpha2, 'beta_target': beta2
            }
        }
        
        # 生成可视化分析
        self.generate_visualizations(p0, p1)
        
        # 生成决策建议
        self.generate_recommendations()
        
        return self.results
    
    def generate_visualizations(self, p0, p1):
        """生成可视化分析图表"""
        
        # 图1：操作特征曲线
        self.plot_oc_curves(p0, p1)
        
        # 图2：功效函数
        self.plot_power_functions(p0, p1)
        
        # 图3：方案比较
        self.plot_plan_comparison()
        
        # 图4：敏感性分析
        self.plot_sensitivity_analysis(p0, p1)
    
    def plot_oc_curves(self, p0, p1):
        """绘制操作特征曲线"""
        plt.figure(figsize=(12, 8))
        
        p_range = np.linspace(0, 0.25, 100)
        
        colors = ['blue', 'red']
        for i, (case_name, case_data) in enumerate(self.results.items()):
            n, c = case_data['n'], case_data['c']
            oc_values = self.calculate_oc_function(n, c, p_range)
            
            plt.plot(p_range, oc_values, color=colors[i], linewidth=2, 
                    label=f'{case_name}: n={n}, c={c}')
        
        # 添加关键点和线
        plt.axvline(x=p0, color='green', linestyle='--', alpha=0.7, 
                   label=f'标称值 p₀={p0}')
        plt.axvline(x=p1, color='orange', linestyle='--', alpha=0.7, 
                   label=f'备择值 p₁={p1}')
        plt.axhline(y=0.95, color='purple', linestyle=':', alpha=0.7, 
                   label='95%接收线')
        plt.axhline(y=0.90, color='brown', linestyle=':', alpha=0.7, 
                   label='90%接收线')
        
        plt.xlabel('真实次品率 p')
        plt.ylabel('接收概率')
        plt.title(f'操作特征曲线 - {self.timestamp}_问题1')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        filename = f'{self.timestamp}_问题1_操作特征曲线.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"已保存：{filename}")
    
    def plot_power_functions(self, p0, p1):
        """绘制功效函数"""
        plt.figure(figsize=(12, 8))
        
        p_range = np.linspace(0, 0.25, 100)
        
        colors = ['blue', 'red']
        for i, (case_name, case_data) in enumerate(self.results.items()):
            n, c = case_data['n'], case_data['c']
            power_values = self.calculate_power_function(n, c, p_range)
            
            plt.plot(p_range, power_values, color=colors[i], linewidth=2,
                    label=f'{case_name}: n={n}, c={c}')
        
        # 添加关键点和线
        plt.axvline(x=p0, color='green', linestyle='--', alpha=0.7,
                   label=f'标称值 p₀={p0}')
        plt.axvline(x=p1, color='orange', linestyle='--', alpha=0.7,
                   label=f'备择值 p₁={p1}')
        plt.axhline(y=0.05, color='purple', linestyle=':', alpha=0.7,
                   label='5%显著性水平')
        plt.axhline(y=0.10, color='brown', linestyle=':', alpha=0.7,
                   label='10%显著性水平')
        
        plt.xlabel('真实次品率 p')
        plt.ylabel('拒收概率（功效）')
        plt.title(f'功效函数 - {self.timestamp}_问题1')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        filename = f'{self.timestamp}_问题1_功效函数.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"已保存：{filename}")
    
    def plot_plan_comparison(self):
        """绘制方案比较图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        case_names = list(self.results.keys())
        
        # 样本量比较
        sample_sizes = [self.results[case]['n'] for case in case_names]
        bars1 = ax1.bar(case_names, sample_sizes, color=['skyblue', 'lightcoral'], alpha=0.7)
        ax1.set_ylabel('样本量')
        ax1.set_title('样本量比较')
        ax1.grid(True, alpha=0.3)
        for bar, value in zip(bars1, sample_sizes):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 5,
                    f'{value}', ha='center', va='bottom', fontweight='bold')
        
        # 临界值比较
        critical_values = [self.results[case]['c'] for case in case_names]
        bars2 = ax2.bar(case_names, critical_values, color=['lightgreen', 'lightyellow'], alpha=0.7)
        ax2.set_ylabel('临界值')
        ax2.set_title('临界值比较')
        ax2.grid(True, alpha=0.3)
        for bar, value in zip(bars2, critical_values):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{value}', ha='center', va='bottom', fontweight='bold')
        
        # 第一类错误概率比较
        alpha_values = [self.results[case]['alpha'] for case in case_names]
        bars3 = ax3.bar(case_names, alpha_values, color=['orange', 'pink'], alpha=0.7)
        ax3.set_ylabel('第一类错误概率')
        ax3.set_title('第一类错误概率比较')
        ax3.grid(True, alpha=0.3)
        for bar, value in zip(bars3, alpha_values):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 第二类错误概率比较
        beta_values = [self.results[case]['beta'] for case in case_names]
        bars4 = ax4.bar(case_names, beta_values, color=['lightsteelblue', 'wheat'], alpha=0.7)
        ax4.set_ylabel('第二类错误概率')
        ax4.set_title('第二类错误概率比较')
        ax4.grid(True, alpha=0.3)
        for bar, value in zip(bars4, beta_values):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        plt.suptitle(f'抽样方案比较分析 - {self.timestamp}_问题1', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        filename = f'{self.timestamp}_问题1_方案比较分析.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"已保存：{filename}")
    
    def plot_sensitivity_analysis(self, p0, p1):
        """绘制敏感性分析"""
        plt.figure(figsize=(14, 10))
        
        # 创建参数变化范围
        p1_range = np.linspace(0.12, 0.20, 20)
        alpha_range = [0.01, 0.05, 0.10]
        
        # 计算不同参数下的样本量
        for alpha in alpha_range:
            sample_sizes = []
            for p1_test in p1_range:
                n, c = self.find_optimal_single_sampling(p0, p1_test, alpha, 0.10)
                sample_sizes.append(n)
            
            plt.plot(p1_range, sample_sizes, marker='o', linewidth=2,
                    label=f'α = {alpha}')
        
        plt.xlabel('备择假设次品率 p₁')
        plt.ylabel('所需样本量')
        plt.title(f'样本量敏感性分析 - {self.timestamp}_问题1')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        filename = f'{self.timestamp}_问题1_敏感性分析.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"已保存：{filename}")
    
    def generate_recommendations(self):
        """生成决策建议"""
        print(f"\n{'='*80}")
        print("决策建议与实施指导")
        print(f"{'='*80}")
        
        print("\n1. 推荐抽样方案：")
        for case_name, case_data in self.results.items():
            print(f"\n   {case_name}：")
            print(f"   - 样本量：{case_data['n']}")
            print(f"   - 临界值：{case_data['c']}")
            if case_name == '情形1':
                print(f"   - 决策规则：当检测到的次品数 ≥ {case_data['c']} 时拒收该批零配件")
            else:
                print(f"   - 决策规则：当检测到的次品数 < {case_data['c']} 时接收该批零配件")
            print(f"   - 第一类错误概率：{case_data['alpha']:.4f}")
            print(f"   - 第二类错误概率：{case_data['beta']:.4f}")
        
        print(f"\n2. 实施建议：")
        print("   - 确保抽样的随机性，避免系统性偏差")
        print("   - 建立标准化的检测流程和记录系统")
        print("   - 定期校准检测设备，确保检测准确性")
        print("   - 根据实际情况调整备择假设值p₁")
        
        print(f"\n3. 成本效益考虑：")
        print("   - 情形1需要更大样本量，检测成本较高但风险控制更严格")
        print("   - 情形2样本量相对较小，适合成本敏感的场景")
        print("   - 建议根据零配件的重要性和成本选择合适的方案")

def main():
    """主函数"""
    analyzer = OptimizedSamplingAnalyzer()
    
    print("开始问题1的优化分析...")
    results = analyzer.comprehensive_analysis()
    
    print(f"\n{'='*80}")
    print("分析完成！")
    print(f"{'='*80}")
    print(f"时间戳：{analyzer.timestamp}")
    print("已生成以下文件：")
    print(f"1. {analyzer.timestamp}_问题1_操作特征曲线.png")
    print(f"2. {analyzer.timestamp}_问题1_功效函数.png")
    print(f"3. {analyzer.timestamp}_问题1_方案比较分析.png")
    print(f"4. {analyzer.timestamp}_问题1_敏感性分析.png")

if __name__ == "__main__":
    main()
