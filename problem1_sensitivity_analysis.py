import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class SensitivityAnalyzer:
    """敏感性分析器"""
    
    def __init__(self):
        self.timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def find_optimal_sample_size(self, p0, p1, alpha, beta):
        """寻找最优样本量"""
        for n in range(10, 1000):
            for c in range(1, n+1):
                alpha_actual = 1 - stats.binom.cdf(c-1, n, p0)
                beta_actual = stats.binom.cdf(c-1, n, p1)
                
                if alpha_actual <= alpha and beta_actual <= beta:
                    return n, c
        return None, None
    
    def analyze_p1_sensitivity(self):
        """分析备择假设p1变化的敏感性"""
        print("分析备择假设p1变化的敏感性...")
        
        p0 = 0.1
        p1_range = np.linspace(0.12, 0.20, 9)  # 减少计算点数
        alpha_values = [0.05, 0.10]
        beta = 0.10
        
        results = []
        
        for alpha in alpha_values:
            sample_sizes = []
            critical_values = []
            
            for p1 in p1_range:
                n, c = self.find_optimal_sample_size(p0, p1, alpha, beta)
                if n is not None:
                    sample_sizes.append(n)
                    critical_values.append(c)
                    results.append({
                        'p1': p1,
                        'alpha': alpha,
                        'n': n,
                        'c': c
                    })
                else:
                    sample_sizes.append(None)
                    critical_values.append(None)
        
        # 绘制敏感性图
        plt.figure(figsize=(14, 6))
        
        # 子图1：样本量变化
        plt.subplot(1, 2, 1)
        for alpha in alpha_values:
            alpha_data = [r for r in results if r['alpha'] == alpha]
            p1_vals = [r['p1'] for r in alpha_data]
            n_vals = [r['n'] for r in alpha_data]
            
            plt.plot(p1_vals, n_vals, marker='o', linewidth=2, 
                    label=f'α = {alpha}')
        
        plt.xlabel('备择假设次品率 p₁')
        plt.ylabel('所需样本量 n')
        plt.title('样本量对p₁的敏感性')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 子图2：临界值变化
        plt.subplot(1, 2, 2)
        for alpha in alpha_values:
            alpha_data = [r for r in results if r['alpha'] == alpha]
            p1_vals = [r['p1'] for r in alpha_data]
            c_vals = [r['c'] for r in alpha_data]
            
            plt.plot(p1_vals, c_vals, marker='s', linewidth=2, 
                    label=f'α = {alpha}')
        
        plt.xlabel('备择假设次品率 p₁')
        plt.ylabel('临界值 c')
        plt.title('临界值对p₁的敏感性')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.suptitle(f'参数敏感性分析 - {self.timestamp}_问题1')
        plt.tight_layout()
        
        filename = f'{self.timestamp}_问题1_敏感性分析.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"已保存：{filename}")
        
        return results
    
    def analyze_alpha_beta_tradeoff(self):
        """分析α和β的权衡关系"""
        print("分析第一类和第二类错误的权衡关系...")
        
        p0 = 0.1
        p1 = 0.15
        
        # 固定样本量，分析α和β的关系
        sample_sizes = [200, 300, 400, 500]
        
        plt.figure(figsize=(12, 8))
        
        for n in sample_sizes:
            alphas = []
            betas = []
            
            for c in range(1, n//4):  # 减少计算范围
                alpha = 1 - stats.binom.cdf(c-1, n, p0)
                beta = stats.binom.cdf(c-1, n, p1)
                
                if alpha <= 0.2 and beta <= 0.5:  # 只考虑合理范围
                    alphas.append(alpha)
                    betas.append(beta)
            
            if alphas and betas:
                plt.plot(alphas, betas, marker='o', linewidth=2, 
                        label=f'n = {n}')
        
        plt.xlabel('第一类错误概率 α')
        plt.ylabel('第二类错误概率 β')
        plt.title(f'α-β权衡关系 - {self.timestamp}_问题1')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 添加参考线
        plt.axvline(x=0.05, color='red', linestyle='--', alpha=0.7, label='α=0.05')
        plt.axvline(x=0.10, color='orange', linestyle='--', alpha=0.7, label='α=0.10')
        plt.axhline(y=0.10, color='green', linestyle='--', alpha=0.7, label='β=0.10')
        
        plt.tight_layout()
        
        filename = f'{self.timestamp}_问题1_错误权衡分析.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"已保存：{filename}")
    
    def cost_benefit_analysis(self):
        """成本效益分析"""
        print("进行成本效益分析...")
        
        # 基本参数
        p0 = 0.1
        p1 = 0.15
        cost_per_sample = 5  # 每个样本检测成本
        cost_false_accept = 10000  # 错误接收的损失
        cost_false_reject = 5000   # 错误拒收的损失
        
        # 不同方案的分析
        scenarios = [
            {'name': '情形1', 'alpha': 0.05, 'beta': 0.10},
            {'name': '情形2', 'alpha': 0.10, 'beta': 0.10},
            {'name': '宽松方案', 'alpha': 0.15, 'beta': 0.15},
        ]
        
        results = []
        
        for scenario in scenarios:
            n, c = self.find_optimal_sample_size(p0, p1, scenario['alpha'], scenario['beta'])
            
            if n is not None:
                # 计算实际错误概率
                alpha_actual = 1 - stats.binom.cdf(c-1, n, p0)
                beta_actual = stats.binom.cdf(c-1, n, p1)
                
                # 计算期望成本
                sampling_cost = n * cost_per_sample
                expected_false_accept_cost = beta_actual * cost_false_accept
                expected_false_reject_cost = alpha_actual * cost_false_reject
                total_expected_cost = sampling_cost + expected_false_accept_cost + expected_false_reject_cost
                
                results.append({
                    'scenario': scenario['name'],
                    'n': n,
                    'c': c,
                    'alpha': alpha_actual,
                    'beta': beta_actual,
                    'sampling_cost': sampling_cost,
                    'false_accept_cost': expected_false_accept_cost,
                    'false_reject_cost': expected_false_reject_cost,
                    'total_cost': total_expected_cost
                })
        
        # 创建成本分析图
        df = pd.DataFrame(results)
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # 成本构成图
        scenarios_names = df['scenario']
        sampling_costs = df['sampling_cost']
        false_accept_costs = df['false_accept_cost']
        false_reject_costs = df['false_reject_cost']
        
        x = np.arange(len(scenarios_names))
        width = 0.6
        
        ax1.bar(x, sampling_costs, width, label='检测成本', alpha=0.8)
        ax1.bar(x, false_accept_costs, width, bottom=sampling_costs, 
               label='错误接收损失', alpha=0.8)
        ax1.bar(x, false_reject_costs, width, 
               bottom=sampling_costs + false_accept_costs,
               label='错误拒收损失', alpha=0.8)
        
        ax1.set_xlabel('方案')
        ax1.set_ylabel('期望成本（元）')
        ax1.set_title('成本构成分析')
        ax1.set_xticks(x)
        ax1.set_xticklabels(scenarios_names)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 总成本比较
        total_costs = df['total_cost']
        bars = ax2.bar(scenarios_names, total_costs, alpha=0.7, 
                      color=['skyblue', 'lightcoral', 'lightgreen'])
        
        ax2.set_xlabel('方案')
        ax2.set_ylabel('总期望成本（元）')
        ax2.set_title('总成本比较')
        ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, value in zip(bars, total_costs):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 50,
                    f'{value:.0f}', ha='center', va='bottom', fontweight='bold')
        
        plt.suptitle(f'成本效益分析 - {self.timestamp}_问题1')
        plt.tight_layout()
        
        filename = f'{self.timestamp}_问题1_成本效益分析.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"已保存：{filename}")
        
        # 打印详细结果
        print("\n成本效益分析结果：")
        print("="*80)
        for result in results:
            print(f"\n{result['scenario']}：")
            print(f"  样本量：{result['n']}")
            print(f"  临界值：{result['c']}")
            print(f"  检测成本：{result['sampling_cost']:.0f}元")
            print(f"  错误接收期望损失：{result['false_accept_cost']:.0f}元")
            print(f"  错误拒收期望损失：{result['false_reject_cost']:.0f}元")
            print(f"  总期望成本：{result['total_cost']:.0f}元")
        
        return results

def main():
    """主函数"""
    analyzer = SensitivityAnalyzer()
    
    print("开始问题1的敏感性分析...")
    print("="*80)
    
    # 1. p1敏感性分析
    sensitivity_results = analyzer.analyze_p1_sensitivity()
    
    # 2. α-β权衡分析
    analyzer.analyze_alpha_beta_tradeoff()
    
    # 3. 成本效益分析
    cost_results = analyzer.cost_benefit_analysis()
    
    print(f"\n{'='*80}")
    print("敏感性分析完成！")
    print(f"{'='*80}")
    print(f"时间戳：{analyzer.timestamp}")
    print("已生成以下文件：")
    print(f"1. {analyzer.timestamp}_问题1_敏感性分析.png")
    print(f"2. {analyzer.timestamp}_问题1_错误权衡分析.png")
    print(f"3. {analyzer.timestamp}_问题1_成本效益分析.png")

if __name__ == "__main__":
    main()
