"""
问题3专项分析：多工序多零配件生产系统
基于表2数据的8零配件2工序生产决策优化
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import networkx as nx
from itertools import product
import seaborn as sns
from scipy.optimize import minimize

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class Problem3Analyzer:
    """问题3专项分析器：多工序多零配件系统"""
    
    def __init__(self):
        self.components_data = self.load_components_data()
        self.production_structure = self.define_production_structure()
        
    def load_components_data(self):
        """加载表2中的8个零配件数据"""
        data = {
            'component_id': [1, 2, 3, 4, 5, 6, 7, 8],
            'defect_rate': [0.10] * 8,  # 所有零配件次品率10%
            'purchase_cost': [10] * 8,   # 所有零配件购买单价10
            'inspection_cost': [2, 8, 12, 2, 8, 12, 8, 12],  # 检测成本
            'assembly_cost': [1, 1, 2, 1, 1, 2, 1, 2],       # 装配成本
            'stage': [1, 1, 1, 2, 2, 2, 3, 3]  # 所属半成品阶段
        }
        return pd.DataFrame(data)
    
    def define_production_structure(self):
        """定义生产结构：8零配件 -> 3半成品 -> 1成品"""
        structure = {
            'semi_product_1': {
                'components': [1, 2, 3],
                'defect_rate': 0.10,
                'inspection_cost': 8,
                'market_price': None,  # 半成品不直接销售
                'replacement_loss': 4
            },
            'semi_product_2': {
                'components': [4, 5, 6], 
                'defect_rate': 0.10,
                'inspection_cost': 8,
                'market_price': None,
                'replacement_loss': 4
            },
            'semi_product_3': {
                'components': [7, 8],
                'defect_rate': 0.10, 
                'inspection_cost': 8,
                'market_price': None,
                'replacement_loss': 4
            },
            'final_product': {
                'components': ['semi_product_1', 'semi_product_2', 'semi_product_3'],
                'defect_rate': 0.10,
                'inspection_cost': 6,
                'market_price': 200,
                'replacement_loss': 30
            }
        }
        return structure
    
    def calculate_stage_quality(self, stage_name, component_decisions, stage_decisions):
        """计算某阶段的质量水平"""
        if stage_name.startswith('semi_product'):
            # 半成品质量计算
            stage_info = self.production_structure[stage_name]
            component_ids = stage_info['components']
            
            # 计算零配件合格率
            component_quality = 1.0
            for comp_id in component_ids:
                comp_idx = comp_id - 1  # 转换为索引
                if component_decisions[comp_idx]:  # 如果检测该零配件
                    # 检测后的合格率（假设检测能发现所有次品）
                    component_quality *= 1.0
                else:
                    # 不检测的合格率
                    component_quality *= (1 - self.components_data.iloc[comp_idx]['defect_rate'])
            
            # 装配后的质量
            assembly_quality = component_quality * (1 - stage_info['defect_rate'])
            
            # 如果检测半成品
            stage_idx = int(stage_name.split('_')[-1]) - 1
            if stage_decisions[stage_idx]:
                return assembly_quality  # 检测后只有合格品通过
            else:
                return assembly_quality  # 不检测则按实际质量
                
        elif stage_name == 'final_product':
            # 最终产品质量计算
            semi_qualities = []
            for i in range(3):
                semi_name = f'semi_product_{i+1}'
                semi_quality = self.calculate_stage_quality(semi_name, component_decisions, stage_decisions)
                semi_qualities.append(semi_quality)
            
            # 最终装配质量
            final_assembly_quality = np.prod(semi_qualities) * (1 - self.production_structure['final_product']['defect_rate'])
            
            # 如果检测最终产品
            if stage_decisions[3]:  # 第4个决策变量是最终产品检测
                return final_assembly_quality
            else:
                return final_assembly_quality
    
    def calculate_total_cost(self, component_decisions, stage_decisions):
        """计算总成本"""
        total_cost = 0
        
        # 零配件购买成本
        total_cost += sum(self.components_data['purchase_cost'])
        
        # 零配件检测成本
        for i, decision in enumerate(component_decisions):
            if decision:
                total_cost += self.components_data.iloc[i]['inspection_cost']
        
        # 装配成本
        total_cost += sum(self.components_data['assembly_cost'])
        
        # 半成品检测成本
        for i in range(3):
            if stage_decisions[i]:
                total_cost += self.production_structure[f'semi_product_{i+1}']['inspection_cost']
        
        # 最终产品检测成本
        if stage_decisions[3]:
            total_cost += self.production_structure['final_product']['inspection_cost']
        
        return total_cost
    
    def calculate_expected_profit(self, component_decisions, stage_decisions):
        """计算期望利润"""
        # 计算总成本
        total_cost = self.calculate_total_cost(component_decisions, stage_decisions)
        
        # 计算最终产品质量
        final_quality = self.calculate_stage_quality('final_product', component_decisions, stage_decisions)
        
        # 收入
        revenue = self.production_structure['final_product']['market_price']
        
        # 调换损失
        replacement_loss = (1 - final_quality) * self.production_structure['final_product']['replacement_loss']
        
        # 期望利润
        expected_profit = revenue - total_cost - replacement_loss
        
        return expected_profit, total_cost, final_quality, replacement_loss
    
    def optimize_decisions(self):
        """优化所有决策变量"""
        print("="*80)
        print("问题3：多工序多零配件生产系统决策优化")
        print("="*80)
        
        best_profit = -float('inf')
        best_decisions = None
        best_details = None
        
        # 枚举所有可能的决策组合
        # 8个零配件检测决策 + 4个阶段检测决策（3个半成品 + 1个最终产品）
        total_decisions = 8 + 4
        
        print(f"正在枚举 2^{total_decisions} = {2**total_decisions} 种决策组合...")
        
        results_list = []
        
        for decisions in product([0, 1], repeat=total_decisions):
            component_decisions = decisions[:8]
            stage_decisions = decisions[8:]
            
            profit, cost, quality, loss = self.calculate_expected_profit(
                component_decisions, stage_decisions
            )
            
            results_list.append({
                'component_decisions': component_decisions,
                'stage_decisions': stage_decisions,
                'profit': profit,
                'cost': cost,
                'quality': quality,
                'replacement_loss': loss
            })
            
            if profit > best_profit:
                best_profit = profit
                best_decisions = decisions
                best_details = (cost, quality, loss)
        
        # 分析结果
        self.analyze_optimization_results(best_decisions, best_profit, best_details, results_list)
        
        return best_decisions, best_profit, results_list
    
    def analyze_optimization_results(self, best_decisions, best_profit, best_details, all_results):
        """分析优化结果"""
        component_decisions = best_decisions[:8]
        stage_decisions = best_decisions[8:]
        cost, quality, loss = best_details
        
        print(f"\n最优决策方案：")
        print("-" * 50)
        
        # 零配件检测决策
        print("零配件检测决策：")
        for i, decision in enumerate(component_decisions):
            comp_data = self.components_data.iloc[i]
            status = "检测" if decision else "不检测"
            print(f"  零配件{i+1}: {status} (检测成本: {comp_data['inspection_cost']})")
        
        # 阶段检测决策
        print("\n阶段检测决策：")
        stage_names = ['半成品1', '半成品2', '半成品3', '最终产品']
        for i, decision in enumerate(stage_decisions):
            status = "检测" if decision else "不检测"
            print(f"  {stage_names[i]}: {status}")
        
        # 经济指标
        print(f"\n经济指标：")
        print(f"  总成本: {cost:.2f}")
        print(f"  最终产品质量: {quality:.1%}")
        print(f"  调换损失: {loss:.2f}")
        print(f"  期望利润: {best_profit:.2f}")
        
        # 生成详细分析
        self.generate_detailed_analysis(all_results)
        
        # 绘制结果可视化
        self.plot_optimization_results(best_decisions, all_results)
    
    def generate_detailed_analysis(self, all_results):
        """生成详细分析报告"""
        print(f"\n详细分析报告：")
        print("-" * 50)
        
        # 转换为DataFrame便于分析
        df_results = pd.DataFrame(all_results)
        
        # 利润分布分析
        print(f"利润分布统计：")
        print(f"  最大利润: {df_results['profit'].max():.2f}")
        print(f"  最小利润: {df_results['profit'].min():.2f}")
        print(f"  平均利润: {df_results['profit'].mean():.2f}")
        print(f"  利润标准差: {df_results['profit'].std():.2f}")
        
        # 质量水平分析
        print(f"\n质量水平统计：")
        print(f"  最高质量: {df_results['quality'].max():.1%}")
        print(f"  最低质量: {df_results['quality'].min():.1%}")
        print(f"  平均质量: {df_results['quality'].mean():.1%}")
        
        # 成本分析
        print(f"\n成本结构统计：")
        print(f"  最高成本: {df_results['cost'].max():.2f}")
        print(f"  最低成本: {df_results['cost'].min():.2f}")
        print(f"  平均成本: {df_results['cost'].mean():.2f}")
        
        # 帕累托前沿分析
        self.analyze_pareto_frontier(df_results)
    
    def analyze_pareto_frontier(self, df_results):
        """分析帕累托前沿"""
        print(f"\n帕累托前沿分析：")
        print("-" * 30)
        
        # 找到帕累托最优解（利润最大化，成本最小化）
        pareto_solutions = []
        
        for i, row_i in df_results.iterrows():
            is_pareto = True
            for j, row_j in df_results.iterrows():
                if i != j:
                    # 如果存在其他解在利润和成本上都不劣于当前解
                    if (row_j['profit'] >= row_i['profit'] and 
                        row_j['cost'] <= row_i['cost'] and
                        (row_j['profit'] > row_i['profit'] or row_j['cost'] < row_i['cost'])):
                        is_pareto = False
                        break
            
            if is_pareto:
                pareto_solutions.append(i)
        
        print(f"发现 {len(pareto_solutions)} 个帕累托最优解")
        
        # 显示前5个帕累托最优解
        pareto_df = df_results.iloc[pareto_solutions].sort_values('profit', ascending=False)
        print("\n前5个帕累托最优解：")
        for i, (idx, row) in enumerate(pareto_df.head().iterrows()):
            print(f"  解{i+1}: 利润={row['profit']:.2f}, 成本={row['cost']:.2f}, 质量={row['quality']:.1%}")
    
    def plot_optimization_results(self, best_decisions, all_results):
        """绘制优化结果可视化"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 转换数据
        df_results = pd.DataFrame(all_results)
        
        # 1. 利润分布直方图
        ax1.hist(df_results['profit'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.axvline(df_results['profit'].max(), color='red', linestyle='--', 
                   label=f'最优利润: {df_results["profit"].max():.2f}')
        ax1.set_xlabel('期望利润')
        ax1.set_ylabel('频次')
        ax1.set_title('期望利润分布')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 成本-利润散点图
        scatter = ax2.scatter(df_results['cost'], df_results['profit'], 
                             c=df_results['quality'], cmap='viridis', alpha=0.6)
        ax2.set_xlabel('总成本')
        ax2.set_ylabel('期望利润')
        ax2.set_title('成本-利润关系（颜色表示质量水平）')
        plt.colorbar(scatter, ax=ax2, label='质量水平')
        ax2.grid(True, alpha=0.3)
        
        # 标记最优解
        best_idx = df_results['profit'].idxmax()
        best_row = df_results.iloc[best_idx]
        ax2.scatter(best_row['cost'], best_row['profit'], 
                   color='red', s=100, marker='*', label='最优解')
        ax2.legend()
        
        # 3. 决策变量重要性分析
        component_importance = []
        stage_importance = []
        
        # 计算每个决策变量被选择的频率
        for i in range(8):  # 零配件决策
            selection_rate = sum(1 for result in all_results 
                                if result['component_decisions'][i]) / len(all_results)
            component_importance.append(selection_rate)
        
        for i in range(4):  # 阶段决策
            selection_rate = sum(1 for result in all_results 
                                if result['stage_decisions'][i]) / len(all_results)
            stage_importance.append(selection_rate)
        
        # 绘制零配件检测重要性
        comp_labels = [f'零配件{i+1}' for i in range(8)]
        bars1 = ax3.bar(comp_labels, component_importance, alpha=0.7, color='lightgreen')
        ax3.set_ylabel('选择频率')
        ax3.set_title('零配件检测决策重要性')
        ax3.tick_params(axis='x', rotation=45)
        ax3.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, importance in zip(bars1, component_importance):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{importance:.1%}', ha='center', va='bottom', fontsize=8)
        
        # 4. 阶段检测重要性
        stage_labels = ['半成品1', '半成品2', '半成品3', '最终产品']
        bars2 = ax4.bar(stage_labels, stage_importance, alpha=0.7, color='lightcoral')
        ax4.set_ylabel('选择频率')
        ax4.set_title('阶段检测决策重要性')
        ax4.tick_params(axis='x', rotation=45)
        ax4.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, importance in zip(bars2, stage_importance):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{importance:.1%}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('problem3_optimization_results.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def draw_production_network(self):
        """绘制生产网络图"""
        plt.figure(figsize=(14, 10))
        
        # 创建网络图
        G = nx.DiGraph()
        
        # 添加节点
        # 零配件节点
        for i in range(8):
            G.add_node(f'C{i+1}', type='component', pos=(i%4*2, 8-i//4*2))
        
        # 半成品节点
        G.add_node('S1', type='semi', pos=(2, 4))
        G.add_node('S2', type='semi', pos=(6, 4))
        G.add_node('S3', type='semi', pos=(10, 4))
        
        # 最终产品节点
        G.add_node('P', type='product', pos=(6, 1))
        
        # 添加边
        # 零配件到半成品
        for i in [1, 2, 3]:
            G.add_edge(f'C{i}', 'S1')
        for i in [4, 5, 6]:
            G.add_edge(f'C{i}', 'S2')
        for i in [7, 8]:
            G.add_edge(f'C{i}', 'S3')
        
        # 半成品到最终产品
        G.add_edge('S1', 'P')
        G.add_edge('S2', 'P')
        G.add_edge('S3', 'P')
        
        # 绘制网络
        pos = nx.get_node_attributes(G, 'pos')
        
        # 绘制不同类型的节点
        component_nodes = [n for n, d in G.nodes(data=True) if d['type'] == 'component']
        semi_nodes = [n for n, d in G.nodes(data=True) if d['type'] == 'semi']
        product_nodes = [n for n, d in G.nodes(data=True) if d['type'] == 'product']
        
        nx.draw_networkx_nodes(G, pos, nodelist=component_nodes, 
                              node_color='lightblue', node_size=800, alpha=0.8)
        nx.draw_networkx_nodes(G, pos, nodelist=semi_nodes, 
                              node_color='lightgreen', node_size=1200, alpha=0.8)
        nx.draw_networkx_nodes(G, pos, nodelist=product_nodes, 
                              node_color='lightcoral', node_size=1500, alpha=0.8)
        
        nx.draw_networkx_edges(G, pos, edge_color='gray', arrows=True, arrowsize=20)
        nx.draw_networkx_labels(G, pos, font_size=10, font_weight='bold')
        
        plt.title('8零配件2工序生产网络结构图', fontsize=16, fontweight='bold')
        plt.axis('off')
        plt.tight_layout()
        plt.savefig('production_network.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    """主函数"""
    analyzer = Problem3Analyzer()
    
    print("问题3专项分析：多工序多零配件生产系统")
    print("基于表2数据的8零配件2工序生产决策优化")
    
    # 绘制生产网络结构
    analyzer.draw_production_network()
    
    # 执行决策优化
    best_decisions, best_profit, all_results = analyzer.optimize_decisions()
    
    print(f"\n分析完成！最优期望利润: {best_profit:.2f}")

if __name__ == "__main__":
    main()
